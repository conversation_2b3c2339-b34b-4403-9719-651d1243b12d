@echo off
echo Testing FlashCpp debugging functionality...

echo.
echo 1. Generating object file with complete debug relocations...
x64\Debug\FlashCpp.exe test_debug.cpp -o test_debug_final.obj
if errorlevel 1 (
    echo ERROR: Failed to generate object file
    exit /b 1
)

echo.
echo 2. Linking with debug information...
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe" /DEBUG:FULL /PDB:test_debug_final.pdb test_debug_final.obj /OUT:test_debug_final.exe
if errorlevel 1 (
    echo ERROR: Failed to link
    exit /b 1
)

echo.
echo 3. Running program to verify functionality...
test_debug_final.exe
set RESULT=%ERRORLEVEL%
echo Program returned: %RESULT%

if %RESULT%==8 (
    echo ✅ SUCCESS: Program executed correctly (3+5=8)
) else (
    echo ❌ ERROR: Program returned %RESULT%, expected 8
    exit /b 1
)

echo.
echo 4. Checking debug information with cvdump...
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\cvdump.exe" (
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\bin\Hostx64\x64\cvdump.exe" test_debug_final.pdb > test_debug_final_cvdump.txt
    echo Debug information dumped to test_debug_final_cvdump.txt
) else (
    echo cvdump.exe not found, skipping PDB validation
)

echo.
echo ✅ ALL TESTS PASSED: FlashCpp debugging functionality is working!
echo.
echo Files generated:
echo - test_debug_final.obj (object file with complete debug relocations)
echo - test_debug_final.exe (executable with debug information)
echo - test_debug_final.pdb (program database for debugging)
echo.
echo To test debugging:
echo 1. Open test_debug_final.exe in Visual Studio debugger
echo 2. Set breakpoints in test_debug.cpp
echo 3. Step through the code to verify source-level debugging works
