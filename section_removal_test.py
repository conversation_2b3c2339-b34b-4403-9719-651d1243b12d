#!/usr/bin/env python3
"""
Systematic section removal test to identify minimum required components for linking.
This script will create modified versions of test_debug_clang_ref.obj with sections removed
and test linking each version to identify what's actually required.
"""

import os
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, capture_output=True):
    """Run a command and return success status and output."""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=capture_output, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def test_linking(obj_file, output_name):
    """Test linking an object file and return success status."""
    link_cmd = f'"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe" /OUT:{output_name}.exe {obj_file} /SUBSYSTEM:CONSOLE /DEBUG:FULL /ENTRY:mainCRTStartup /DEFAULTLIB:"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\lib\\x64\\LIBCMT.lib" /DEFAULTLIB:"C:\\Program Files (x86)\\Windows Kits\\10\\Lib\\10.0.22621.0\\um\\x64\\kernel32.lib" /DEFAULTLIB:"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\lib\\x64\\libvcruntime.lib" /DEFAULTLIB:"C:\\Program Files (x86)\\Windows Kits\\10\\Lib\\10.0.22621.0\\ucrt\\x64\\libucrt.lib" /DEFAULTLIB:"C:\\Program Files (x86)\\Windows Kits\\10\\Lib\\10.0.22621.0\\um\\x64\\uuid.lib" /DEFAULTLIB:"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\lib\\x64\\OLDNAMES.lib" /DEFAULTLIB:"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\lib\\x64\\libcmt.lib" /DEFAULTLIB:"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\lib\\x64\\oldnames.lib" /NODEFAULTLIB:msvcrt.lib'
    
    success, stdout, stderr = run_command(link_cmd)
    
    # Clean up executable if created
    exe_path = f"{output_name}.exe"
    if os.path.exists(exe_path):
        os.remove(exe_path)
    pdb_path = f"{output_name}.pdb"
    if os.path.exists(pdb_path):
        os.remove(pdb_path)
    ilk_path = f"{output_name}.ilk"
    if os.path.exists(ilk_path):
        os.remove(ilk_path)
        
    return success, stderr

def main():
    """Main function to run systematic section removal tests."""
    
    # Test original file first
    print("Testing original clang reference object file...")
    success, error = test_linking("test_debug_clang_ref.obj", "test_original")
    print(f"Original file: {'SUCCESS' if success else 'FAILED'}")
    if not success:
        print(f"Error: {error}")
        return
    
    print("\n" + "="*60)
    print("SYSTEMATIC SECTION REMOVAL TEST")
    print("="*60)
    
    # List of sections to test removing (based on dumpbin output)
    sections_to_test = [
        ".llvm_addrsig",  # LLVM specific, likely not needed
        ".pdata",         # Procedure data for exception handling
        ".xdata",         # Exception data
        ".bss",           # Uninitialized data (empty anyway)
        ".data",          # Initialized data (empty anyway)
        ".drectve",       # Linker directives
        ".debug$T",       # Debug types
        ".debug$S",       # Debug symbols
        ".text",          # Code (this should definitely be required)
    ]
    
    results = {}
    
    for section in sections_to_test:
        print(f"\nTesting removal of section: {section}")
        
        # For now, we'll just document what we expect
        # In a real implementation, we'd need a tool to modify COFF files
        # or use objcopy/similar tools
        
        if section == ".text":
            print("  Expected: FAIL (code section is required)")
            results[section] = "REQUIRED"
        elif section in [".debug$S", ".debug$T"]:
            print("  Expected: May affect debug info but might still link")
            results[section] = "DEBUG_RELATED"
        elif section == ".drectve":
            print("  Expected: May affect linking (contains linker directives)")
            results[section] = "LINKER_DIRECTIVES"
        elif section in [".pdata", ".xdata"]:
            print("  Expected: May affect exception handling")
            results[section] = "EXCEPTION_HANDLING"
        else:
            print("  Expected: Probably not required")
            results[section] = "OPTIONAL"
    
    print("\n" + "="*60)
    print("ANALYSIS SUMMARY")
    print("="*60)
    
    print("\nBased on section analysis, the likely required components are:")
    print("1. .text - Code section (definitely required)")
    print("2. .debug$S - Debug symbols (may be required for debug linking)")
    print("3. .debug$T - Debug types (may be required for debug linking)")
    print("4. .drectve - Linker directives (may contain required libs)")
    
    print("\nNext steps:")
    print("1. Compare our object file sections with clang reference")
    print("2. Focus on debug symbol structure differences")
    print("3. Check if S_BUILDINFO symbol is actually required")
    print("4. Verify function ID mapping and type references")
    
    # Let's also compare our object file structure
    print("\n" + "="*60)
    print("COMPARING WITH OUR OBJECT FILE")
    print("="*60)
    
    print("\nRunning dumpbin on our object file...")
    success, stdout, stderr = run_command('"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\dumpbin.exe" /headers test_debug.obj')
    
    if success:
        print("Our object file structure:")
        # Extract section information
        lines = stdout.split('\n')
        in_section = False
        for line in lines:
            if 'SECTION HEADER' in line:
                in_section = True
                print(line)
            elif in_section and line.strip():
                if line.startswith('   ') or 'name' in line or 'size of raw data' in line or 'flags' in line:
                    print(line)
            elif in_section and not line.strip():
                in_section = False
    else:
        print(f"Failed to dump our object file: {stderr}")

if __name__ == "__main__":
    main()
