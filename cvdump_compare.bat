@echo off
echo ========================================
echo CVDump Comparison for Debug Information
echo ========================================

echo.
echo Dumping FlashCpp generated object file...
.\cvdump.exe test_debug.obj > test_debug_cvdump_new.txt 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: cvdump failed for test_debug.obj
) else (
    echo SUCCESS: test_debug_cvdump_new.txt created
)

echo.
echo Dumping MSVC reference object file...
.\cvdump.exe test_debug_ref.obj > test_debug_ref_cvdump_new.txt 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: cvdump failed for test_debug_ref.obj
) else (
    echo SUCCESS: test_debug_ref_cvdump_new.txt created
)

echo.
echo Comparison files created:
echo - test_debug_cvdump_new.txt (FlashCpp)
echo - test_debug_ref_cvdump_new.txt (MSVC Reference)
echo.
echo Use a text comparison tool to analyze differences.
