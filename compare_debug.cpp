#include <iostream>
#include <fstream>
#include <vector>
#include <cstdint>
#include <iomanip>

void parse_debug_section(const std::string& filename) {
    std::ifstream file(filename, std::ios::binary);
    if (!file) {
        std::cout << "Cannot open " << filename << std::endl;
        return;
    }
    
    // Read entire file
    file.seekg(0, std::ios::end);
    size_t size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    std::vector<uint8_t> data(size);
    file.read(reinterpret_cast<char*>(data.data()), size);
    
    std::cout << "\n=== " << filename << " ===" << std::endl;
    
    // Simple COFF header parsing to find .debug$S section
    if (size < 20) return;
    
    uint16_t machine = *reinterpret_cast<const uint16_t*>(data.data());
    uint16_t num_sections = *reinterpret_cast<const uint16_t*>(data.data() + 2);
    
    std::cout << "Machine: 0x" << std::hex << machine << ", Sections: " << std::dec << num_sections << std::endl;
    
    // Skip to section headers (after COFF header)
    size_t section_header_offset = 20; // COFF header size
    
    for (int i = 0; i < num_sections; i++) {
        size_t offset = section_header_offset + i * 40; // Each section header is 40 bytes
        if (offset + 40 > size) break;
        
        // Read section name (8 bytes)
        char section_name[9] = {0};
        memcpy(section_name, data.data() + offset, 8);
        
        uint32_t section_size = *reinterpret_cast<const uint32_t*>(data.data() + offset + 16);
        uint32_t section_data_offset = *reinterpret_cast<const uint32_t*>(data.data() + offset + 20);
        
        if (strcmp(section_name, ".debug$S") == 0) {
            std::cout << "Found .debug$S section: size=" << section_size << ", offset=" << section_data_offset << std::endl;
            
            if (section_data_offset + section_size <= size) {
                const uint8_t* debug_data = data.data() + section_data_offset;
                
                // Skip 4-byte signature
                const uint8_t* ptr = debug_data + 4;
                const uint8_t* end = debug_data + section_size;
                
                while (ptr < end - 8) {
                    uint32_t kind = *reinterpret_cast<const uint32_t*>(ptr);
                    uint32_t length = *reinterpret_cast<const uint32_t*>(ptr + 4);
                    
                    std::cout << "Subsection Kind: " << kind << ", Length: " << length << std::endl;
                    
                    if (length == 0 || length > (end - ptr - 8)) break;
                    
                    ptr += 8;
                    const uint8_t* subsection_start = ptr;
                    
                    if (kind == 241) { // Symbols subsection
                        const uint8_t* subsection_end = ptr + length;
                        while (ptr < subsection_end - 4) {
                            uint16_t record_length = *reinterpret_cast<const uint16_t*>(ptr);
                            uint16_t record_kind = *reinterpret_cast<const uint16_t*>(ptr + 2);
                            
                            if (record_kind == 0x1111) { // S_REGREL32
                                std::cout << "  S_REGREL32 record:" << std::endl;
                                
                                // Show raw bytes
                                std::cout << "    Raw bytes: ";
                                for (int j = 0; j < std::min(20, (int)(subsection_end - ptr)); j++) {
                                    std::cout << std::hex << std::setw(2) << std::setfill('0') << (int)ptr[j] << " ";
                                }
                                std::cout << std::dec << std::endl;
                                
                                if (ptr + 14 < subsection_end) {
                                    uint32_t offset = *reinterpret_cast<const uint32_t*>(ptr + 4);
                                    uint32_t type_index = *reinterpret_cast<const uint32_t*>(ptr + 8);
                                    uint16_t register_id = *reinterpret_cast<const uint16_t*>(ptr + 12);
                                    
                                    std::cout << "    Offset: 0x" << std::hex << offset << std::endl;
                                    std::cout << "    Type: 0x" << std::hex << type_index << std::endl;
                                    std::cout << "    Register: 0x" << std::hex << register_id << " (" << std::dec << register_id << ")" << std::endl;
                                    
                                    // Read parameter name
                                    const uint8_t* name_ptr = ptr + 14;
                                    std::string name;
                                    while (name_ptr < subsection_end && *name_ptr != 0) {
                                        name += static_cast<char>(*name_ptr++);
                                    }
                                    std::cout << "    Name: " << name << std::endl;
                                }
                            }
                            
                            // Advance to next record
                            size_t total_record_size = record_length + 2;
                            if (ptr + total_record_size > subsection_end) break;
                            ptr += total_record_size;
                        }
                    } else {
                        // Skip other subsections
                        ptr = subsection_start + length;
                    }
                    
                    // Align to 4-byte boundary
                    while ((reinterpret_cast<uintptr_t>(ptr) & 3) != 0 && ptr < end) {
                        ptr++;
                    }
                }
            }
        }
    }
}

int main() {
    parse_debug_section("../../test_debug.obj");
    parse_debug_section("../../test_debug_ref.obj");
    return 0;
}
