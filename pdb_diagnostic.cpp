#include <iostream>
#include <fstream>
#include <vector>
#include <iomanip>
#include <cstring>
#include <string>

struct COFFHeader {
    uint16_t machine;
    uint16_t numberOfSections;
    uint32_t timeDateStamp;
    uint32_t pointerToSymbolTable;
    uint32_t numberOfSymbols;
    uint16_t sizeOfOptionalHeader;
    uint16_t characteristics;
};

struct SectionHeader {
    char name[8];
    uint32_t virtualSize;
    uint32_t virtualAddress;
    uint32_t sizeOfRawData;
    uint32_t pointerToRawData;
    uint32_t pointerToRelocations;
    uint32_t pointerToLinenumbers;
    uint16_t numberOfRelocations;
    uint16_t numberOfLinenumbers;
    uint32_t characteristics;
};

void hexDump(const uint8_t* data, size_t size, size_t offset = 0) {
    for (size_t i = 0; i < size; i += 16) {
        std::cout << std::hex << std::setw(8) << std::setfill('0') << (offset + i) << ": ";
        
        // Print hex bytes
        for (size_t j = 0; j < 16; ++j) {
            if (i + j < size) {
                std::cout << std::hex << std::setw(2) << std::setfill('0') << (int)data[i + j] << " ";
            } else {
                std::cout << "   ";
            }
        }
        
        std::cout << " ";
        
        // Print ASCII
        for (size_t j = 0; j < 16 && i + j < size; ++j) {
            char c = data[i + j];
            std::cout << (isprint(c) ? c : '.');
        }
        
        std::cout << std::endl;
    }
    std::cout << std::dec;
}

void analyzeDebugSection(const std::string& filename, const std::string& sectionName) {
    std::ifstream file(filename, std::ios::binary);
    if (!file) {
        std::cout << "Cannot open " << filename << std::endl;
        return;
    }
    
    // Read COFF header
    COFFHeader coffHeader;
    file.read(reinterpret_cast<char*>(&coffHeader), sizeof(coffHeader));
    
    std::cout << "\n=== " << filename << " ===" << std::endl;
    std::cout << "Machine: 0x" << std::hex << coffHeader.machine << std::endl;
    std::cout << "Sections: " << std::dec << coffHeader.numberOfSections << std::endl;
    
    // Read section headers
    std::vector<SectionHeader> sections(coffHeader.numberOfSections);
    file.read(reinterpret_cast<char*>(sections.data()), sizeof(SectionHeader) * coffHeader.numberOfSections);
    
    // Find debug section
    for (const auto& section : sections) {
        std::string name(section.name, 8);
        name = name.substr(0, name.find('\0'));
        
        if (name == sectionName) {
            std::cout << "\nFound " << sectionName << " section:" << std::endl;
            std::cout << "Size: " << section.sizeOfRawData << " bytes" << std::endl;
            std::cout << "Offset: 0x" << std::hex << section.pointerToRawData << std::endl;
            
            // Read section data
            std::vector<uint8_t> data(section.sizeOfRawData);
            file.seekg(section.pointerToRawData);
            file.read(reinterpret_cast<char*>(data.data()), section.sizeOfRawData);
            
            std::cout << "\nFirst 256 bytes of " << sectionName << ":" << std::endl;
            hexDump(data.data(), std::min((size_t)256, (size_t)section.sizeOfRawData));
            
            if (sectionName == ".debug$S") {
                // Parse debug$S structure
                std::cout << "\nParsing .debug$S structure:" << std::endl;
                
                if (data.size() >= 4) {
                    uint32_t signature = *reinterpret_cast<const uint32_t*>(data.data());
                    std::cout << "Signature: 0x" << std::hex << signature << std::endl;
                    
                    const uint8_t* ptr = data.data() + 4;
                    const uint8_t* end = data.data() + data.size();
                    
                    while (ptr < end - 8) {
                        uint32_t kind = *reinterpret_cast<const uint32_t*>(ptr);
                        uint32_t length = *reinterpret_cast<const uint32_t*>(ptr + 4);
                        
                        std::cout << "Subsection Kind: " << std::dec << kind << " (0x" << std::hex << kind << ")";
                        std::cout << ", Length: " << std::dec << length << std::endl;
                        
                        if (length == 0 || length > (end - ptr - 8)) break;
                        
                        ptr += 8 + length;
                        
                        // Align to 4-byte boundary
                        while ((reinterpret_cast<uintptr_t>(ptr) & 3) != 0 && ptr < end) {
                            ptr++;
                        }
                    }
                }
            }
            
            return;
        }
    }
    
    std::cout << "Section " << sectionName << " not found" << std::endl;
}

void compareFiles(const std::string& file1, const std::string& file2) {
    std::cout << "\n========================================" << std::endl;
    std::cout << "PDB DIAGNOSTIC COMPARISON" << std::endl;
    std::cout << "========================================" << std::endl;
    
    analyzeDebugSection(file1, ".debug$S");
    analyzeDebugSection(file2, ".debug$S");
    
    analyzeDebugSection(file1, ".debug$T");
    analyzeDebugSection(file2, ".debug$T");
}

int main(int argc, char* argv[]) {
    if (argc == 1) {
        // Default comparison
        compareFiles("test_debug.obj", "test_debug_ref.obj");
        std::cout << "\n\nAlso comparing with Clang reference:" << std::endl;
        compareFiles("test_debug.obj", "test_debug_clang_ref.obj");
    } else if (argc == 3) {
        compareFiles(argv[1], argv[2]);
    } else {
        std::cout << "Usage: " << argv[0] << " [file1] [file2]" << std::endl;
        std::cout << "If no arguments provided, compares test_debug.obj with references" << std::endl;
        return 1;
    }
    
    return 0;
}
