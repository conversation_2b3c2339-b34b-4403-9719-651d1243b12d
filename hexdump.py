#!/usr/bin/env python3
import sys

def hexdump(filename, start=0, length=100):
    with open(filename, 'rb') as f:
        f.seek(start)
        data = f.read(length)
        
    for i in range(0, len(data), 16):
        chunk = data[i:i+16]
        hex_part = ' '.join(f'{b:02X}' for b in chunk)
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
        print(f'{start+i:08X}: {hex_part:<48} {ascii_part}')

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("Usage: python hexdump.py <filename> [start] [length]")
        sys.exit(1)
    
    filename = sys.argv[1]
    start = int(sys.argv[2]) if len(sys.argv) > 2 else 0
    length = int(sys.argv[3]) if len(sys.argv) > 3 else 100
    
    hexdump(filename, start, length)
