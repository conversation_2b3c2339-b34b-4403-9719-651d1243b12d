@echo off
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe" /OUT:test_debug_flashcpp_final.exe test_debug_flashcpp_two_pointers.obj /SUBSYSTEM:CONSOLE /DEBUG:FULL /ENTRY:mainCRTStartup /DEFAULTLIB:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib" /DEFAULTLIB:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.22621.0\um\x64\kernel32.lib" /DEFAULTLIB:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib" /DEFAULTLIB:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.22621.0\ucrt\x64\libucrt.lib" /DEFAULTLIB:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.22621.0\um\x64\uuid.lib" /DEFAULTLIB:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib" /DEFAULTLIB:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcmt.lib" /DEFAULTLIB:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\oldnames.lib" /NODEFAULTLIB:msvcrt.lib

echo.
echo Linking completed with errorlevel %ERRORLEVEL%

if exist test_debug_flashcpp_five_pointers.exe (
    echo Running test_debug_flashcpp_simple_pointer.exe...
    test_debug_flashcpp_simple_pointer.exe
    echo Program exited with errorlevel %ERRORLEVEL%
) else (
    echo ERROR: test_debug_flashcpp_simple_pointer.exe was not created!
)
