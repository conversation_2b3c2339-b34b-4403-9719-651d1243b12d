#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
#include <set>
#include "src/coffi/coffi.hpp"

int main() {
    try {
        // Load the clang reference object file
        COFFI::coffi obj;
        std::string input_file = "test_debug_clang_ref.obj";
        std::string output_file = "test_debug_clang_ref_filtered.obj";
        
        std::cout << "Loading " << input_file << "..." << std::endl;
        if (!obj.load(input_file)) {
            std::cerr << "Error: Failed to load " << input_file << std::endl;
            return 1;
        }
        
        // Get sections
        auto& sections = const_cast<COFFI::sections&>(obj.get_sections());
        
        std::cout << "\nOriginal sections (" << sections.size() << " total):" << std::endl;
        for (size_t i = 0; i < sections.size(); ++i) {
            const auto* section = sections[i];
            std::cout << "  [" << i << "] " << section->get_name() 
                      << " (size: " << section->get_data_size() 
                      << ", flags: 0x" << std::hex << section->get_flags() << std::dec << ")" << std::endl;
        }
        
        // Define sections to keep
        std::set<std::string> sections_to_keep = {
            ".debug$S",
            ".debug$T", 
            ".drectve",
            ".text"
        };

        std::set<std::string> sections_to_remove = {
            //".pdata",
        };
        
        std::cout << "\nSections to remove:" << std::endl;
        for (const auto& name : sections_to_remove) {
            std::cout << "  " << name << std::endl;
        }
        
        // Remove sections that are not in the keep list
        // We need to iterate backwards to avoid index issues when erasing
        std::cout << "\nRemoving sections..." << std::endl;
        for (int i = static_cast<int>(sections.size()) - 1; i >= 0; --i) {
            const auto* section = sections[i];
            std::string section_name = section->get_name();
            
            if (sections_to_remove.find(section_name) != sections_to_remove.end()) {
                std::cout << "  Removing section: " << section_name << std::endl;
                delete section;  // Free the memory
                sections.erase(sections.begin() + i);
            } else {
                std::cout << "  Keeping section: " << section_name << std::endl;
            }
        }
        
        // Update section indices after removal
        for (size_t i = 0; i < sections.size(); ++i) {
            sections[i]->set_index(static_cast<uint32_t>(i));
        }
        
        // Update the COFF header to reflect the new section count
        auto* header = const_cast<COFFI::coff_header*>(obj.get_header());
        if (header) {
            header->set_sections_count(static_cast<uint16_t>(sections.size()));
            std::cout << "\nUpdated COFF header section count to: " << sections.size() << std::endl;
        }
        
        std::cout << "\nFinal sections (" << sections.size() << " total):" << std::endl;
        for (size_t i = 0; i < sections.size(); ++i) {
            const auto* section = sections[i];
            std::cout << "  [" << i << "] " << section->get_name() 
                      << " (size: " << section->get_data_size() 
                      << ", flags: 0x" << std::hex << section->get_flags() << std::dec << ")" << std::endl;
        }
        
        // Save the modified object file
        std::cout << "\nSaving to " << output_file << "..." << std::endl;
        if (!obj.save(output_file)) {
            std::cerr << "Error: Failed to save " << output_file << std::endl;
            return 1;
        }
        
        std::cout << "Successfully created " << output_file << " with filtered sections!" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }
}
