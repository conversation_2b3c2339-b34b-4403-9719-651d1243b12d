#!/usr/bin/env python3
import struct

def parse_section_header(data, offset):
    # COFF section header is 40 bytes
    header = struct.unpack('<8sIIIIIIHHI', data[offset:offset+40])
    name = header[0].rstrip(b'\x00').decode('ascii', errors='ignore')
    virtual_size = header[1]
    virtual_address = header[2] 
    size_of_raw_data = header[3]
    ptr_to_raw_data = header[4]
    ptr_to_relocs = header[5]
    ptr_to_line_nums = header[6]
    num_relocs = header[7]
    num_line_nums = header[8]
    characteristics = header[9]
    return name, characteristics

# Check Clang reference file
with open('test_debug_clang_ref.obj', 'rb') as f:
    data = f.read()
    
# Find .debug$T section in Clang
debug_t_pos = data.find(b'.debug$T')
if debug_t_pos != -1:
    # Section header starts before the name
    section_start = debug_t_pos - 8  # Name is at offset 8 in section header
    name, characteristics = parse_section_header(data, section_start)
    print(f'Clang .debug$T section characteristics: 0x{characteristics:08X}')
    
    # Decode alignment
    align_bits = (characteristics >> 20) & 0xF
    if align_bits == 0:
        align = 'default'
    else:
        align = f'{1 << (align_bits - 1)} bytes'
    print(f'Clang .debug$T alignment: {align}')

# Check our file
with open('test_debug_flashcpp_final.obj', 'rb') as f:
    data = f.read()
    
debug_t_pos = data.find(b'.debug$T')
if debug_t_pos != -1:
    section_start = debug_t_pos - 8
    name, characteristics = parse_section_header(data, section_start)
    print(f'FlashCpp .debug$T section characteristics: 0x{characteristics:08X}')
    
    # Decode alignment
    align_bits = (characteristics >> 20) & 0xF
    if align_bits == 0:
        align = 'default'
    else:
        align = f'{1 << (align_bits - 1)} bytes'
    print(f'FlashCpp .debug$T alignment: {align}')
