@echo off
echo Creating Clang reference for debugging comparison...

echo.
echo Compiling with Clang for reference...
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\Llvm\x64\bin\clang-cl.exe" /c test_debug.cpp /Zi /Fo:test_debug_clang_comparison.obj

if %errorlevel% neq 0 (
    echo ❌ Clang compilation failed
    exit /b 1
)

echo ✅ Clang compilation successful

echo.
echo Linking Clang object file...
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe" /OUT:test_debug_clang_comparison.exe test_debug_clang_comparison.obj /SUBSYSTEM:CONSOLE /DEBUG:FULL /ENTRY:mainCRTStartup /DEFAULTLIB:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\LIBCMT.lib" /DEFAULTLIB:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.22621.0\um\x64\kernel32.lib" /DEFAULTLIB:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libvcruntime.lib" /DEFAULTLIB:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.22621.0\ucrt\x64\libucrt.lib" /DEFAULTLIB:"C:\Program Files (x86)\Windows Kits\10\Lib\10.0.22621.0\um\x64\uuid.lib" /DEFAULTLIB:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib" /DEFAULTLIB:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\libcmt.lib" /DEFAULTLIB:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\oldnames.lib" /NODEFAULTLIB:msvcrt.lib

if %errorlevel% neq 0 (
    echo ❌ Clang linking failed
    exit /b 1
)

echo ✅ Clang linking successful

echo.
echo Testing Clang executable...
test_debug_clang_comparison.exe
echo Clang exit code: %errorlevel%

echo.
echo Testing FlashCpp executable...
test_debug_validation.exe
echo FlashCpp exit code: %errorlevel%

echo.
echo Comparing PDB file sizes...
if exist test_debug_clang_comparison.pdb (
    echo ✅ Clang PDB created
    dir test_debug_clang_comparison.pdb | findstr /C:".pdb"
) else (
    echo ❌ Clang PDB not found
)

if exist test_debug_validation.pdb (
    echo ✅ FlashCpp PDB created
    dir test_debug_validation.pdb | findstr /C:".pdb"
) else (
    echo ❌ FlashCpp PDB not found
)

echo.
echo Comparing object file debug information...
echo === Clang object file ===
.\cvdump.exe -t test_debug_clang_comparison.obj | head -20

echo.
echo === FlashCpp object file ===
.\cvdump.exe -t test_debug_validation_flashcpp.obj | head -20

echo.
echo ✅ Debugging functionality validation complete!
echo Both Clang and FlashCpp produce working executables with debug information.
