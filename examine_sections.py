#!/usr/bin/env python3
"""
Examine .xdata and .pdata sections in COFF object files
"""

import struct
import sys

def read_coff_header(data):
    """Read COFF header from object file"""
    if len(data) < 20:
        return None
    
    machine, num_sections, timestamp, sym_ptr, num_syms, opt_size, characteristics = \
        struct.unpack('<HHIIIHH', data[:20])
    
    return {
        'machine': machine,
        'num_sections': num_sections,
        'timestamp': timestamp,
        'sym_ptr': sym_ptr,
        'num_syms': num_syms,
        'opt_size': opt_size,
        'characteristics': characteristics
    }

def read_section_headers(data, num_sections):
    """Read section headers from object file"""
    sections = []
    offset = 20  # After COFF header
    
    for i in range(num_sections):
        if offset + 40 > len(data):
            break
            
        section_data = data[offset:offset + 40]
        name = section_data[:8].rstrip(b'\x00').decode('ascii', errors='ignore')
        
        virtual_size, virtual_addr, raw_size, raw_ptr, reloc_ptr, line_ptr, \
        num_relocs, num_lines, characteristics = \
            struct.unpack('<IIIIIIHHI', section_data[8:])
        
        sections.append({
            'name': name,
            'virtual_size': virtual_size,
            'virtual_addr': virtual_addr,
            'raw_size': raw_size,
            'raw_ptr': raw_ptr,
            'reloc_ptr': reloc_ptr,
            'line_ptr': line_ptr,
            'num_relocs': num_relocs,
            'num_lines': num_lines,
            'characteristics': characteristics
        })
        
        offset += 40
    
    return sections

def hex_dump(data, title="Data"):
    """Create hex dump of binary data"""
    print(f"\n{title}:")
    if not data:
        print("  (empty)")
        return
        
    for i in range(0, len(data), 16):
        hex_part = ' '.join(f'{b:02x}' for b in data[i:i+16])
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
        print(f'  {i:04x}: {hex_part:<48} {ascii_part}')

def read_relocations(data, section):
    """Read relocations for a section"""
    if section['num_relocs'] == 0:
        return []
    
    relocations = []
    offset = section['reloc_ptr']
    
    for i in range(section['num_relocs']):
        if offset + 10 > len(data):
            break
            
        # COFF relocation entry: virtual_address(4) + symbol_index(4) + type(2)
        virtual_addr, symbol_index, reloc_type = struct.unpack('<IIH', data[offset:offset+10])
        
        relocations.append({
            'virtual_addr': virtual_addr,
            'symbol_index': symbol_index,
            'type': reloc_type
        })
        
        offset += 10
    
    return relocations

def analyze_xdata(data):
    """Analyze .xdata section content (x64 unwind information)"""
    if len(data) == 0:
        return "Empty .xdata section"
    
    analysis = []
    analysis.append(f"Size: {len(data)} bytes")
    
    if len(data) >= 4:
        # x64 unwind info structure
        # Byte 0: Version (3 bits) + Flags (5 bits)
        # Byte 1: Size of prolog
        # Byte 2: Count of unwind codes
        # Byte 3: Frame register (4 bits) + Frame register offset (4 bits)
        
        version_flags = data[0]
        version = version_flags & 0x07
        flags = (version_flags >> 3) & 0x1F
        prolog_size = data[1]
        unwind_code_count = data[2]
        frame_reg_info = data[3]
        frame_reg = frame_reg_info & 0x0F
        frame_offset = (frame_reg_info >> 4) & 0x0F
        
        analysis.append(f"Unwind Info:")
        analysis.append(f"  Version: {version}")
        analysis.append(f"  Flags: 0x{flags:02x}")
        analysis.append(f"  Prolog size: {prolog_size}")
        analysis.append(f"  Unwind code count: {unwind_code_count}")
        analysis.append(f"  Frame register: {frame_reg}")
        analysis.append(f"  Frame offset: {frame_offset}")
        
        # Decode flags
        flag_names = []
        if flags & 0x01: flag_names.append("UNW_FLAG_EHANDLER")
        if flags & 0x02: flag_names.append("UNW_FLAG_UHANDLER") 
        if flags & 0x04: flag_names.append("UNW_FLAG_CHAININFO")
        if flag_names:
            analysis.append(f"  Flag meanings: {', '.join(flag_names)}")
    
    return '\n'.join(analysis)

def analyze_pdata(data):
    """Analyze .pdata section content (x64 procedure data)"""
    if len(data) == 0:
        return "Empty .pdata section"
    
    analysis = []
    analysis.append(f"Size: {len(data)} bytes")
    
    # .pdata contains RUNTIME_FUNCTION entries (12 bytes each on x64)
    num_entries = len(data) // 12
    analysis.append(f"Number of RUNTIME_FUNCTION entries: {num_entries}")
    
    for i in range(num_entries):
        offset = i * 12
        if offset + 12 <= len(data):
            begin_addr, end_addr, unwind_info = struct.unpack('<III', data[offset:offset+12])
            analysis.append(f"  Entry {i}:")
            analysis.append(f"    Begin Address: 0x{begin_addr:08x}")
            analysis.append(f"    End Address: 0x{end_addr:08x}")
            analysis.append(f"    Unwind Info: 0x{unwind_info:08x}")
    
    return '\n'.join(analysis)

def examine_file(filename):
    """Examine .xdata and .pdata sections in a COFF file"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        return
    
    print(f"=" * 80)
    print(f"EXAMINING: {filename}")
    print("=" * 80)
    
    # Parse COFF header
    header = read_coff_header(data)
    if not header:
        print("Error: Invalid COFF header")
        return
    
    print(f"Machine: 0x{header['machine']:04x}")
    print(f"Sections: {header['num_sections']}")
    print(f"Symbols: {header['num_syms']}")
    
    # Parse sections
    sections = read_section_headers(data, header['num_sections'])
    
    # Examine .xdata and .pdata sections
    for section_name in ['.xdata', '.pdata']:
        section = next((s for s in sections if s['name'] == section_name), None)
        
        if section:
            print(f"\n{'-' * 60}")
            print(f"SECTION: {section_name}")
            print(f"{'-' * 60}")
            print(f"Size: {section['raw_size']} bytes")
            print(f"Relocations: {section['num_relocs']}")
            print(f"Characteristics: 0x{section['characteristics']:08x}")
            
            # Read section data
            if section['raw_size'] > 0:
                section_data = data[section['raw_ptr']:section['raw_ptr'] + section['raw_size']]
                
                # Analyze content
                if section_name == '.xdata':
                    print(f"\n{analyze_xdata(section_data)}")
                elif section_name == '.pdata':
                    print(f"\n{analyze_pdata(section_data)}")
                
                # Show hex dump
                hex_dump(section_data, f"Raw {section_name} data")
                
                # Show relocations
                if section['num_relocs'] > 0:
                    relocations = read_relocations(data, section)
                    print(f"\nRelocations:")
                    for i, reloc in enumerate(relocations):
                        print(f"  {i}: Offset=0x{reloc['virtual_addr']:04x}, Symbol={reloc['symbol_index']}, Type=0x{reloc['type']:04x}")
            else:
                print("Section is empty")
        else:
            print(f"\n{section_name}: Not found")

def main():
    if len(sys.argv) == 1:
        # Examine all reference files
        files = ["test_debug.obj", "test_debug_ref.obj", "test_debug_clang_ref.obj"]
        for filename in files:
            examine_file(filename)
            print("\n")
    else:
        for filename in sys.argv[1:]:
            examine_file(filename)

if __name__ == "__main__":
    main()
