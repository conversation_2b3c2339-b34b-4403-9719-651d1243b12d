@echo off
echo Testing line number information with addr2line...

echo.
echo Getting function addresses from the executable...
"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\dumpbin.exe" /disasm test_debug_validation.exe | findstr /C:"add" /C:"main"

echo.
echo Checking if addr2line is available...
where addr2line >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ addr2line found
    echo Testing line number resolution...
    REM Test with a known address - this would need the actual addresses from disasm
    echo Note: Need actual addresses from disassembly for precise testing
) else (
    echo ❌ addr2line not found in PATH
    echo This is expected - addr2line is typically part of MinGW/MSYS2
)

echo.
echo Alternative: Using Visual Studio debugger commands...
echo Creating debugger script to test line numbers...

echo .lines > debug_script.txt
echo lm >> debug_script.txt
echo x test_debug_validation!add >> debug_script.txt
echo x test_debug_validation!main >> debug_script.txt
echo q >> debug_script.txt

echo.
echo Debug script created. To test manually:
echo 1. Open test_debug_validation.exe in Visual Studio
echo 2. Set breakpoints on lines 2 and 7 (function entry points)
echo 3. Set breakpoints on lines 3 and 8 (return statements)
echo 4. Verify that breakpoints are hit correctly
echo 5. Check that source file is displayed correctly
