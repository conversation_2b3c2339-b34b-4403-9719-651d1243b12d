#include <iostream>
#include <cstdint>

// Replicate the structures
enum class TypeRecordKind : uint16_t {
    LF_FUNC_ID = 0x1601,
};

struct TypeRecordHeader {
    uint16_t length;
    TypeRecordKind kind;
};

int main() {
    std::cout << "sizeof(TypeRecordHeader): " << sizeof(TypeRecordHeader) << std::endl;
    std::cout << "sizeof(TypeRecordKind): " << sizeof(TypeRecordKind) << std::endl;
    std::cout << "sizeof(uint16_t): " << sizeof(uint16_t) << std::endl;

    // Test the exact calculation we're using
    size_t content_size = 12; // Example: add function data
    size_t total_record_size = sizeof(TypeRecordHeader) + content_size;
    size_t aligned_record_size = (total_record_size + 3) & ~3;
    size_t padding_bytes = aligned_record_size - total_record_size;

    std::cout << "\nExample calculation for 12-byte content:" << std::endl;
    std::cout << "content_size: " << content_size << std::endl;
    std::cout << "sizeof(TypeRecordHeader): " << sizeof(TypeRecordHeader) << std::endl;
    std::cout << "total_record_size: " << total_record_size << std::endl;
    std::cout << "aligned_record_size: " << aligned_record_size << std::endl;
    std::cout << "padding_bytes: " << padding_bytes << std::endl;
    std::cout << "final length: " << (content_size + padding_bytes) << std::endl;

    return 0;
}
