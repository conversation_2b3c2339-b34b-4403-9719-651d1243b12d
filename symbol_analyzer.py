#!/usr/bin/env python3
"""
Symbol table analyzer for COFF object files
Analyzes and compares symbol tables between FlashCpp and reference files
"""

import struct
import sys
import os

def read_coff_header(data):
    """Read COFF header from object file"""
    if len(data) < 20:
        return None
    
    machine, num_sections, timestamp, sym_ptr, num_syms, opt_size, characteristics = \
        struct.unpack('<HHIIIHH', data[:20])
    
    return {
        'machine': machine,
        'num_sections': num_sections,
        'timestamp': timestamp,
        'sym_ptr': sym_ptr,
        'num_syms': num_syms,
        'opt_size': opt_size,
        'characteristics': characteristics
    }

def read_string_table(data, sym_ptr, num_syms):
    """Read string table from object file"""
    # String table starts after symbol table
    # Each symbol is 18 bytes
    string_table_offset = sym_ptr + (num_syms * 18)
    
    if string_table_offset + 4 > len(data):
        return {}
    
    # First 4 bytes are string table size
    string_table_size = struct.unpack('<I', data[string_table_offset:string_table_offset + 4])[0]
    
    if string_table_offset + string_table_size > len(data):
        return {}
    
    string_data = data[string_table_offset + 4:string_table_offset + string_table_size]
    
    # Parse strings
    strings = {}
    offset = 4  # Start after size field
    current_pos = 0
    
    while current_pos < len(string_data):
        # Find null terminator
        end_pos = current_pos
        while end_pos < len(string_data) and string_data[end_pos] != 0:
            end_pos += 1
        
        if end_pos < len(string_data):
            string_value = string_data[current_pos:end_pos].decode('ascii', errors='ignore')
            strings[offset + current_pos] = string_value
            current_pos = end_pos + 1
        else:
            break
    
    return strings

def read_symbols(data, header):
    """Read symbol table from object file"""
    if header['sym_ptr'] == 0 or header['num_syms'] == 0:
        return []
    
    symbols = []
    string_table = read_string_table(data, header['sym_ptr'], header['num_syms'])
    
    offset = header['sym_ptr']
    
    for i in range(header['num_syms']):
        if offset + 18 > len(data):
            break
        
        symbol_data = data[offset:offset + 18]
        
        # COFF symbol table entry:
        # 0-7: Name (8 bytes) or string table offset
        # 8-11: Value (4 bytes)
        # 12-13: Section number (2 bytes)
        # 14-15: Type (2 bytes)
        # 16: Storage class (1 byte)
        # 17: Number of auxiliary symbols (1 byte)
        
        name_data = symbol_data[:8]
        value, section_num, sym_type, storage_class, aux_count = \
            struct.unpack('<IHHBB', symbol_data[8:])
        
        # Determine symbol name
        if name_data[:4] == b'\x00\x00\x00\x00':
            # Long name - offset into string table
            string_offset = struct.unpack('<I', name_data[4:8])[0]
            name = string_table.get(string_offset, f"<string_offset_{string_offset}>")
        else:
            # Short name - stored directly
            name = name_data.rstrip(b'\x00').decode('ascii', errors='ignore')
        
        symbols.append({
            'index': i,
            'name': name,
            'value': value,
            'section_num': section_num,
            'type': sym_type,
            'storage_class': storage_class,
            'aux_count': aux_count
        })
        
        offset += 18
        
        # Skip auxiliary symbols
        if aux_count > 0:
            offset += aux_count * 18
            i += aux_count
    
    return symbols

def analyze_symbols(filename):
    """Analyze symbols in a COFF object file"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        return
    
    header = read_coff_header(data)
    if not header:
        print(f"Error: Invalid COFF header in {filename}")
        return
    
    symbols = read_symbols(data, header)
    
    print(f"\nSYMBOL ANALYSIS: {filename}")
    print("=" * 60)
    print(f"Total symbols: {len(symbols)}")
    print(f"Header symbol count: {header['num_syms']}")
    
    # Group symbols by type
    symbol_types = {}
    for sym in symbols:
        storage_class = sym['storage_class']
        if storage_class not in symbol_types:
            symbol_types[storage_class] = []
        symbol_types[storage_class].append(sym)
    
    # Storage class names
    storage_class_names = {
        0: "NULL",
        1: "AUTOMATIC", 
        2: "EXTERNAL",
        3: "STATIC",
        4: "REGISTER",
        5: "EXTERNAL_DEF",
        6: "LABEL",
        7: "UNDEFINED_LABEL",
        8: "MEMBER_OF_STRUCT",
        9: "ARGUMENT",
        10: "STRUCT_TAG",
        11: "MEMBER_OF_UNION",
        12: "UNION_TAG",
        13: "TYPE_DEFINITION",
        14: "UNDEFINED_STATIC",
        15: "ENUM_TAG",
        16: "MEMBER_OF_ENUM",
        17: "REGISTER_PARAM",
        18: "BIT_FIELD",
        100: "BLOCK",
        101: "FUNCTION",
        102: "END_OF_STRUCT",
        103: "FILE",
        104: "SECTION",
        105: "WEAK_EXTERNAL",
        107: "CLR_TOKEN"
    }
    
    print("\nSYMBOLS BY STORAGE CLASS:")
    for storage_class in sorted(symbol_types.keys()):
        class_name = storage_class_names.get(storage_class, f"UNKNOWN_{storage_class}")
        symbols_in_class = symbol_types[storage_class]
        print(f"\n{class_name} ({storage_class}): {len(symbols_in_class)} symbols")
        
        for sym in symbols_in_class:
            section_name = "ABS" if sym['section_num'] == 0xFFFF else f"sect_{sym['section_num']}" if sym['section_num'] > 0 else "UNDEF"
            print(f"  [{sym['index']:2d}] {sym['name']:<20} value=0x{sym['value']:08x} section={section_name} type=0x{sym['type']:04x} aux={sym['aux_count']}")

def compare_symbols(file1, file2):
    """Compare symbols between two files"""
    print(f"\nCOMPARING SYMBOLS: {file1} vs {file2}")
    print("=" * 80)
    
    # Read both files
    try:
        with open(file1, 'rb') as f:
            data1 = f.read()
        with open(file2, 'rb') as f:
            data2 = f.read()
    except FileNotFoundError as e:
        print(f"Error: {e}")
        return
    
    header1 = read_coff_header(data1)
    header2 = read_coff_header(data2)
    
    if not header1 or not header2:
        print("Error: Invalid COFF headers")
        return
    
    symbols1 = read_symbols(data1, header1)
    symbols2 = read_symbols(data2, header2)
    
    print(f"{file1}: {len(symbols1)} symbols")
    print(f"{file2}: {len(symbols2)} symbols")
    
    # Find symbols in file1 but not in file2
    names1 = {sym['name'] for sym in symbols1}
    names2 = {sym['name'] for sym in symbols2}
    
    only_in_1 = names1 - names2
    only_in_2 = names2 - names1
    common = names1 & names2
    
    print(f"\nCommon symbols: {len(common)}")
    print(f"Only in {file1}: {len(only_in_1)}")
    print(f"Only in {file2}: {len(only_in_2)}")
    
    if only_in_2:
        print(f"\nSymbols missing from {file1}:")
        for name in sorted(only_in_2):
            # Find the symbol details
            sym = next(s for s in symbols2 if s['name'] == name)
            print(f"  {name} (storage_class={sym['storage_class']}, section={sym['section_num']})")

def main():
    if len(sys.argv) == 2:
        analyze_symbols(sys.argv[1])
    elif len(sys.argv) == 3:
        compare_symbols(sys.argv[1], sys.argv[2])
    else:
        print("Usage:")
        print("  python symbol_analyzer.py <file>           - Analyze symbols in file")
        print("  python symbol_analyzer.py <file1> <file2>  - Compare symbols between files")

if __name__ == "__main__":
    main()
