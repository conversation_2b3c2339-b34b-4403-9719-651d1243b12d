# FlashCpp Debugging Analysis - Systematic Fix Plan

## 🔍 CRITICAL ISSUES IDENTIFIED

### Issue #1: Function Code Size Mismatch ⚠️ HIGH PRIORITY
**Problem**: <PERSON><PERSON><PERSON> generates larger function code than Clang
- <PERSON><PERSON><PERSON> add(): 26 bytes vs Clang: 17 bytes (+9 bytes)
- <PERSON><PERSON>pp main(): 38 bytes vs Clang: 33 bytes (+5 bytes)

**Root Cause**: Different calling conventions and frame pointer usage
- FlashCpp: RBP frame pointer + parameter storage to stack
- Clang: RSP-only + register optimization

**Impact**: Debugger expects specific code sizes for line mapping

### Issue #2: Missing Type Information 🚨 CRITICAL
**Problem**: FlashCpp .debug$T section is 18x smaller than Clang's
- FlashCpp: 93 bytes (6 type records)
- Clang: 1708 bytes (hundreds of type records)

**Missing Components**:
- LF_STRING_ID records for build info
- LF_BUILDINFO record
- Fundamental type definitions (int, void, char, etc.)

### Issue #3: Function Name Mangling
**Problem**: <PERSON><PERSON>pp uses unmangled names, <PERSON><PERSON> uses C++ mangling
- <PERSON>Cpp: `add`, `main`
- Clang: `?add@@YAHHH@Z`, `main`

## 🎯 SYSTEMATIC FIX PLAN

### Phase 1: Fix Function Code Size (IMMEDIATE)
**Goal**: Make FlashCpp generate same code size as Clang

**Actions**:
1. Analyze Clang's RSP-only calling convention
2. Modify FlashCpp code generation to match
3. Remove unnecessary stack allocations
4. Optimize register usage

**Expected Result**: Function sizes match exactly

### Phase 2: Add Missing Type Information (CRITICAL)
**Goal**: Generate complete type information like Clang

**Actions**:
1. Add fundamental type definitions (int=0x74, void=0x3, etc.)
2. Generate LF_STRING_ID records for build info
3. Add LF_BUILDINFO record after functions
4. Match Clang's type generation order

**Expected Result**: .debug$T section size approaches Clang's

### Phase 3: Test Debugging Functionality (VALIDATION)
**Goal**: Verify actual debugger compatibility

**Actions**:
1. Test with Visual Studio debugger
2. Test breakpoint setting on functions
3. Test variable inspection
4. Test call stack display
