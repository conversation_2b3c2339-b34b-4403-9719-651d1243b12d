#!/usr/bin/env python3
"""
Binary comparison tool for PDB debugging
Compares debug sections between FlashCpp and reference object files
"""

import struct
import sys
import os

def read_coff_header(data):
    """Read COFF header from object file"""
    if len(data) < 20:
        return None
    
    machine, num_sections, timestamp, sym_ptr, num_syms, opt_size, characteristics = \
        struct.unpack('<HHIIIHH', data[:20])
    
    return {
        'machine': machine,
        'num_sections': num_sections,
        'timestamp': timestamp,
        'sym_ptr': sym_ptr,
        'num_syms': num_syms,
        'opt_size': opt_size,
        'characteristics': characteristics
    }

def read_section_headers(data, num_sections):
    """Read section headers from object file"""
    sections = []
    offset = 20  # After COFF header
    
    for i in range(num_sections):
        if offset + 40 > len(data):
            break
            
        section_data = data[offset:offset + 40]
        name = section_data[:8].rstrip(b'\x00').decode('ascii', errors='ignore')
        virtual_size, virtual_addr, raw_size, raw_ptr, reloc_ptr, line_ptr, \
        num_relocs, num_lines, characteristics = \
            struct.unpack('<IIIIIIHHH', section_data[8:])
        
        sections.append({
            'name': name,
            'virtual_size': virtual_size,
            'virtual_addr': virtual_addr,
            'raw_size': raw_size,
            'raw_ptr': raw_ptr,
            'reloc_ptr': reloc_ptr,
            'line_ptr': line_ptr,
            'num_relocs': num_relocs,
            'num_lines': num_lines,
            'characteristics': characteristics
        })
        
        offset += 40
    
    return sections

def hex_dump(data, offset=0, max_bytes=256):
    """Create hex dump of binary data"""
    lines = []
    for i in range(0, min(len(data), max_bytes), 16):
        hex_part = ' '.join(f'{b:02x}' for b in data[i:i+16])
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
        lines.append(f'{offset + i:08x}: {hex_part:<48} {ascii_part}')
    return '\n'.join(lines)

def analyze_debug_section(data, section):
    """Analyze debug section structure"""
    if section['raw_size'] == 0:
        return "Section is empty"
    
    section_data = data[section['raw_ptr']:section['raw_ptr'] + section['raw_size']]
    
    analysis = []
    analysis.append(f"Section: {section['name']}")
    analysis.append(f"Size: {section['raw_size']} bytes")
    analysis.append(f"Relocations: {section['num_relocs']}")
    analysis.append(f"Characteristics: 0x{section['characteristics']:08x}")
    
    if section['name'] == '.debug$S':
        # Parse debug$S structure
        if len(section_data) >= 4:
            signature = struct.unpack('<I', section_data[:4])[0]
            analysis.append(f"Signature: 0x{signature:08x}")
            
            # Parse subsections
            ptr = 4
            subsection_count = 0
            while ptr < len(section_data) - 8 and subsection_count < 10:
                kind, length = struct.unpack('<II', section_data[ptr:ptr+8])
                analysis.append(f"  Subsection {subsection_count}: Kind={kind} (0x{kind:x}), Length={length}")
                
                ptr += 8 + length
                # Align to 4-byte boundary
                ptr = (ptr + 3) & ~3
                subsection_count += 1
    
    elif section['name'] == '.debug$T':
        # Parse debug$T structure
        analysis.append(f"Type information section")
        if len(section_data) >= 4:
            # First 4 bytes are usually signature
            signature = struct.unpack('<I', section_data[:4])[0]
            analysis.append(f"Signature: 0x{signature:08x}")
    
    analysis.append("\nFirst 128 bytes:")
    analysis.append(hex_dump(section_data, section['raw_ptr'], 128))
    
    return '\n'.join(analysis)

def compare_files(file1, file2):
    """Compare two object files"""
    print("=" * 80)
    print("BINARY COMPARISON ANALYSIS")
    print("=" * 80)
    
    # Read both files
    try:
        with open(file1, 'rb') as f:
            data1 = f.read()
        with open(file2, 'rb') as f:
            data2 = f.read()
    except FileNotFoundError as e:
        print(f"Error: {e}")
        return
    
    # Parse COFF headers
    header1 = read_coff_header(data1)
    header2 = read_coff_header(data2)
    
    if not header1 or not header2:
        print("Error: Invalid COFF headers")
        return
    
    print(f"\nFile 1: {file1}")
    print(f"  Machine: 0x{header1['machine']:04x}")
    print(f"  Sections: {header1['num_sections']}")
    print(f"  Symbols: {header1['num_syms']}")
    
    print(f"\nFile 2: {file2}")
    print(f"  Machine: 0x{header2['machine']:04x}")
    print(f"  Sections: {header2['num_sections']}")
    print(f"  Symbols: {header2['num_syms']}")
    
    # Parse sections
    sections1 = read_section_headers(data1, header1['num_sections'])
    sections2 = read_section_headers(data2, header2['num_sections'])
    
    # Compare debug sections
    debug_sections = ['.debug$S', '.debug$T', '.xdata', '.pdata']
    
    for section_name in debug_sections:
        print(f"\n{'=' * 60}")
        print(f"ANALYZING {section_name}")
        print('=' * 60)
        
        # Find sections in both files
        sec1 = next((s for s in sections1 if s['name'] == section_name), None)
        sec2 = next((s for s in sections2 if s['name'] == section_name), None)
        
        if sec1:
            print(f"\n{file1}:")
            print(analyze_debug_section(data1, sec1))
        else:
            print(f"\n{file1}: {section_name} not found")
        
        if sec2:
            print(f"\n{file2}:")
            print(analyze_debug_section(data2, sec2))
        else:
            print(f"\n{file2}: {section_name} not found")
        
        # Compare if both exist
        if sec1 and sec2:
            print(f"\nCOMPARISON:")
            print(f"  Size: {sec1['raw_size']} vs {sec2['raw_size']} bytes")
            print(f"  Relocations: {sec1['num_relocs']} vs {sec2['num_relocs']}")
            
            if sec1['raw_size'] > 0 and sec2['raw_size'] > 0:
                data1_sec = data1[sec1['raw_ptr']:sec1['raw_ptr'] + min(sec1['raw_size'], 64)]
                data2_sec = data2[sec2['raw_ptr']:sec2['raw_ptr'] + min(sec2['raw_size'], 64)]
                
                if data1_sec == data2_sec:
                    print("  First 64 bytes: IDENTICAL")
                else:
                    print("  First 64 bytes: DIFFERENT")
                    # Show byte-by-byte differences
                    for i in range(min(len(data1_sec), len(data2_sec))):
                        if data1_sec[i] != data2_sec[i]:
                            print(f"    Byte {i}: 0x{data1_sec[i]:02x} vs 0x{data2_sec[i]:02x}")
                            if i > 10:  # Limit output
                                print("    ... (more differences)")
                                break

def main():
    if len(sys.argv) == 1:
        # Default comparison
        compare_files("test_debug.obj", "test_debug_ref.obj")
        print("\n\n" + "=" * 80)
        print("CLANG REFERENCE COMPARISON")
        print("=" * 80)
        compare_files("test_debug.obj", "test_debug_clang_ref.obj")
    elif len(sys.argv) == 3:
        compare_files(sys.argv[1], sys.argv[2])
    else:
        print("Usage: python binary_compare.py [file1] [file2]")
        print("If no arguments provided, compares test_debug.obj with references")

if __name__ == "__main__":
    main()
