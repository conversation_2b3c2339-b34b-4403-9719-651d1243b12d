@echo off
cd /d "C:\Projects\FlashCpp"

echo Compiling section_filter.cpp...

REM Set up Visual Studio environment
rem call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

REM Compile with proper include paths
cl.exe /std:c++20 /EHsc /I"src" section_filter.cpp /Fe:section_filter.exe

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    exit /b %ERRORLEVEL%
)

echo Compilation successful!
echo Running section_filter.exe...
echo.

section_filter.exe

echo.
echo Done!
